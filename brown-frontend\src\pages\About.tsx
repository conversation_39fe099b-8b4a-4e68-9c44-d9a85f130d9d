import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, Award, BookOpen, Users, Calendar } from "lucide-react";

const About = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });
  const [bioRef, bioInView] = useInView({ threshold: 0.1, triggerOnce: true });
  const [achievementsRef, achievementsInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? "translateY(0px)" : "translateY(50px)",
  });

  const bioSpring = useSpring({
    opacity: bioInView ? 1 : 0,
    transform: bioInView ? "translateY(0px)" : "translateY(30px)",
  });

  const achievementsSpring = useSpring({
    opacity: achievementsInView ? 1 : 0,
    transform: achievementsInView ? "translateY(0px)" : "translateY(30px)",
  });

  const handleWorkWithMe = () => {
    window.open("/contact", "_self");
  };

  return (
    <div className="min-h-screen bg-cream-50">
      {/* Hero Section */}
      <animated.section
        ref={heroRef}
        style={heroSpring}
        className="py-20 px-4 sm:px-6 lg:px-8"
      >
        <div className="max-w-4xl mx-auto text-center">
          <div className="mb-8">
            <img
              src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face"
              alt="Sarah Brown - Author and Writing Coach"
              className="w-48 h-48 rounded-full mx-auto object-cover shadow-lg"
              loading="lazy"
            />
          </div>
          <h1 className="text-4xl md:text-6xl font-serif text-brown-900 mb-6">
            Meet Sarah Brown
          </h1>
          <p className="text-xl text-brown-700 max-w-2xl mx-auto leading-relaxed">
            Author, Writing Coach, and Storytelling Advocate helping writers
            find their voice and share their message with the world.
          </p>
        </div>
      </animated.section>

      {/* Bio Section */}
      <animated.section
        ref={bioRef}
        style={bioSpring}
        className="py-16 px-4 sm:px-6 lg:px-8 bg-white"
      >
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-serif text-brown-900 mb-8 text-center">
            My Story
          </h2>
          <div className="prose prose-lg max-w-none text-brown-800 leading-relaxed">
            <p className="mb-6">
              I remember having this 40 or 60-leaves book back in secondary
              school. That was 2014. I was in SS2 and I wrote stories in that
              notebook. A few friends read them and I was so proud of those
              stories. Then the book got missing. It was painful. So painful
              that I haven't forgotten the feeling.
            </p>
            <p className="mb-6">
              Still in that 2014, I won a national essay writing competition.
              The way the newspapers put it, they were shocked a public school
              pupil had come out tops. I still giggle when I read that headline.
            </p>
            <p className="mb-6">
              As a teen, I wrote for fun. I wrote to relieve boredom. Writing
              was how I went to places I wished I could be. As soon as the
              English Language teacher said, "Write a letter to your uncle in
              London," it was my lucky day!
            </p>
            <p className="mb-6">
              I wasn't bad at accounting as an undergrad, but during the
              pandemic in 2020, when we were home for 9 months, I had nothing to
              do but read. And write. And pray. I like to tell my friends that
              the pandemic slowed us down long enough to find ourselves.
            </p>
            <p className="mb-6">
              In the last quarter of 2021, the Lord had simply said to me, "I
              want you to teach story writing." Look where we are now. I
              consider writing a change-provoking art. Books have molded my life
              and the lives of many before now.
            </p>
          </div>
        </div>
      </animated.section>

      {/* Achievements Section */}
      <animated.section
        ref={achievementsRef}
        style={achievementsSpring}
        className="py-16 px-4 sm:px-6 lg:px-8"
      >
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-serif text-brown-900 mb-12 text-center">
            Key Achievements
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent>
                <div className="text-3xl font-bold text-brown-800 mb-2">5+</div>
                <div className="text-brown-600">Years of Experience</div>
              </CardContent>
            </Card>
            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent>
                <div className="text-3xl font-bold text-brown-800 mb-2">12</div>
                <div className="text-brown-600">Books Published</div>
              </CardContent>
            </Card>
            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent>
                <div className="text-3xl font-bold text-brown-800 mb-2">
                  200+
                </div>
                <div className="text-brown-600">Writers Coached</div>
              </CardContent>
            </Card>
            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent>
                <div className="text-3xl font-bold text-brown-800 mb-2">8</div>
                <div className="text-brown-600">Best Selling Books</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </animated.section>

      {/* Press Mentions */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-serif text-brown-900 mb-12 text-center">
            Press & Recognition
          </h2>
          <div className="space-y-6">
            <Card className="p-6">
              <CardContent>
                <blockquote className="text-lg italic text-brown-700 mb-4">
                  "Sarah Brown's approach to writing coaching is transformative.
                  She doesn't just teach technique; she helps writers find their
                  authentic voice."
                </blockquote>
                <cite className="text-brown-600">
                  — Literary Review Magazine
                </cite>
              </CardContent>
            </Card>
            <Card className="p-6">
              <CardContent>
                <blockquote className="text-lg italic text-brown-700 mb-4">
                  "A rising star in the world of writing education, Brown
                  Patience Company is setting new standards for author
                  development."
                </blockquote>
                <cite className="text-brown-600">— Writers' Weekly</cite>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-brown-100">
        <div className="max-w-2xl mx-auto text-center">
          <h2 className="text-3xl font-serif text-brown-900 mb-6">
            Ready to Work Together?
          </h2>
          <p className="text-lg text-brown-700 mb-8">
            Let's bring your writing dreams to life. Whether you need coaching,
            editing, or guidance on your publishing journey, I'm here to help.
          </p>
          <Button
            onClick={handleWorkWithMe}
            size="lg"
            className="bg-brown-800 hover:bg-brown-900 text-white px-8 py-3 text-lg"
          >
            Work with Me
          </Button>
        </div>
      </section>
    </div>
  );
};

export default About;
