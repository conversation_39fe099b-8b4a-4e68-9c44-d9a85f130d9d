import { useEffect } from "react";
import { useSpring, animated } from "@react-spring/web";
import { useInView } from "react-intersection-observer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  BookOpen,
  Edit3,
  FileText,
  CheckCircle,
  Clock,
  Users,
  Star,
  ArrowRight,
  PenTool,
  Target,
  Award,
  MessageCircle,
  SyringeIcon,
} from "lucide-react";

const BookWritingEditing = () => {
  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [servicesRef, servicesInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const [processRef, processInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  useEffect(() => {
    document.title =
      "Book Writing & Editing Services - The Brown Patience Company";
  }, []);

  const heroSpring = useSpring({
    opacity: heroInView ? 1 : 0,
    transform: heroInView ? "translateY(0px)" : "translateY(50px)",
  });

  const servicesSpring = useSpring({
    opacity: servicesInView ? 1 : 0,
    transform: servicesInView ? "translateY(0px)" : "translateY(50px)",
  });

  const processSpring = useSpring({
    opacity: processInView ? 1 : 0,
    transform: processInView ? "translateY(0px)" : "translateY(50px)",
  });

  const editingServices = [
    {
      title: "Experienced Professionals, Coaches & Speakers",
      description:
        "Make your powerful speeches and wealth of experience into life-changing books that further testify of your authority in your field, reaching even more people. ",
      icon: BookOpen,
    },
    {
      title: "Visionaries & Change Agents",
      description:
        "Turn your burning message into a compelling call to action. Sell your great vision to those who will be willing to run with it. Get support for your cause.",
      icon: Target,
    },
    {
      title: "Empaths & Survivors",
      description:
        "Share your experience with those who are currently where you've been. Show them how you got through it all. Show them how to get through it too.",
      icon: SyringeIcon,
    },
    {
      title: "Authors Who Need Assistance",
      description:
        "You've been writing for a while but it's been feeling flat. Inject the necessary impact into your voice; rebrand your writing; make your subsequent books sharp and provoking, as they should be.",
      icon: Edit3,
    },
  ];

  const writingProcess = [
    {
      step: "1",
      title: "Initial Consultation",
      description:
        "We discuss your project goals, timeline, and specific needs to create a customized plan.",
      icon: MessageCircle,
    },
    {
      step: "2",
      title: "Project Assessment",
      description:
        "I review your manuscript or project outline to provide detailed feedback and recommendations.",
      icon: FileText,
    },
    {
      step: "3",
      title: "Collaborative Writing/Editing",
      description:
        "We work together through multiple rounds of revisions to achieve your vision.",
      icon: Users,
    },
    {
      step: "4",
      title: "Final Polish",
      description:
        "Final review and refinement to ensure your book is publication-ready.",
      icon: Award,
    },
  ];

  const scrollToContact = () => {
    document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <div className="min-h-screen bg-cream-50 pt-16">
      {/* Hero Section */}
      <section
        ref={heroRef}
        className="py-20 bg-gradient-to-br from-brand-primary to-brand-neutral/20"
      >
        <div className="container mx-auto px-4">
          <animated.div
            style={heroSpring}
            className="max-w-4xl mx-auto text-center"
          >
            <div className="inline-flex items-center gap-2 bg-brand-accent/10 text-brand-accent px-4 py-2 rounded-full text-sm font-medium mb-6">
              <BookOpen className="w-4 h-4" />
              Professional Writing & Editing Services
            </div>
            <h1 className="text-4xl md:text-6xl font-serif font-bold text-brand-secondary mb-6">
              Book Writing & Editing
            </h1>
            <p className="text-xl text-brand-secondary/70 mb-8 leading-relaxed">
              You know it's important to have a pair of professional eyes go
              through your manuscript. Or you need someone to help you put your
              thoughts together, to help you make a book. From developmental
              editing to final proofreading, I provide comprehensive services
              that honor your unique voice while ensuring professional quality.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="hero"
                size="lg"
                onClick={scrollToContact}
                className="group"
              >
                Start Your Project
                <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button variant="outline" size="lg">
                View Portfolio
              </Button>
            </div>
          </animated.div>
        </div>
      </section>

      {/* Services Section */}
      <section ref={servicesRef} className="py-20">
        <div className="container mx-auto px-4">
          <animated.div style={servicesSpring}>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                Who can benefit from this book writing and editing service?
              </h2>
              <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                "If you have a great message or story to share, and will like to
                put it out in book format, reach Brown Patience. She's got
                really great skills in books writing, editing and finishing. Her
                services are superb." — Timi Oshinowo
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {editingServices.map((service, index) => {
                const IconComponent = service.icon;
                return (
                  <animated.div
                    key={service.title}
                    style={{
                      opacity: servicesInView ? 1 : 0,
                      transform: servicesInView
                        ? "translateY(0px)"
                        : "translateY(50px)",
                      transitionDelay: servicesInView
                        ? `${200 + index * 100}ms`
                        : "0ms",
                      transition: "all 0.6s ease-out",
                    }}
                  >
                    <Card className="h-full hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
                      <CardHeader>
                        <div className="flex items-center gap-4 mb-4">
                          <div className="w-12 h-12 bg-brand-accent/10 rounded-xl flex items-center justify-center">
                            <IconComponent className="w-6 h-6 text-brand-accent" />
                          </div>
                          <div>
                            <h3 className="text-xl font-serif font-bold text-brand-secondary">
                              {service.title}
                            </h3>
                          </div>
                        </div>
                        <p className="text-brand-secondary/70 leading-relaxed">
                          {service.description}
                        </p>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div className="flex items-start gap-3">
                            <CheckCircle className="w-4 h-4 text-brand-accent mt-0.5 flex-shrink-0" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </animated.div>
                );
              })}
            </div>
          </animated.div>
        </div>
      </section>

      {/* Process Section */}
      <section ref={processRef} className="py-20 bg-brand-neutral/30">
        <div className="container mx-auto px-4">
          <animated.div style={processSpring}>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-4">
                My Writing & Editing Process
              </h2>
              <p className="text-lg text-brand-secondary/70 max-w-2xl mx-auto">
                A collaborative approach that ensures your vision comes to life
                while maintaining the highest standards.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {writingProcess.map((step, index) => {
                const IconComponent = step.icon;
                return (
                  <animated.div
                    key={step.step}
                    style={{
                      opacity: processInView ? 1 : 0,
                      transform: processInView
                        ? "translateY(0px)"
                        : "translateY(50px)",
                      transitionDelay: processInView
                        ? `${200 + index * 100}ms`
                        : "0ms",
                      transition: "all 0.6s ease-out",
                    }}
                    className="text-center"
                  >
                    <div className="relative mb-6">
                      <div className="w-16 h-16 bg-brand-accent rounded-full flex items-center justify-center mx-auto mb-4">
                        <IconComponent className="w-8 h-8 text-brand-primary" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-brand-secondary text-brand-primary rounded-full flex items-center justify-center text-sm font-bold">
                        {step.step}
                      </div>
                    </div>
                    <h3 className="text-lg font-serif font-bold text-brand-secondary mb-3">
                      {step.title}
                    </h3>
                    <p className="text-brand-secondary/70 text-sm leading-relaxed">
                      {step.description}
                    </p>
                  </animated.div>
                );
              })}
            </div>
          </animated.div>
        </div>
      </section>

      {/* Philosophy Section */}
      <section className="py-20 bg-brand-neutral/30">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-8">
              Why Writing Matters
            </h2>
            <div className="bg-brand-primary rounded-2xl p-8 md:p-12 shadow-elegant">
              <p className="text-lg text-brand-secondary/80 leading-relaxed mb-6 italic">
                "I consider writing a change-provoking art. Books have molded my
                life and the lives of many before now. Every person who has ever
                had anything to say has encouraged us to glean the wisdom in
                books."
              </p>
              <p className="text-lg text-brand-secondary/80 leading-relaxed">
                Writing is a change-provoking art. It's why we read. It's why we
                write. It's why The Brown Patience Company exists. To ensure you
                have all the help you need to share the message you need to
                share — clearly, compellingly. And then the harvest! We wait for
                the harvest in the lives of people. For surely, the harvest
                comes.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="bg-brand-primary rounded-2xl p-8 md:p-12 shadow-elegant max-w-4xl mx-auto text-center">
            <h3 className="text-3xl md:text-4xl font-serif font-bold text-brand-secondary mb-6">
              Ready to Share Your Message?
            </h3>
            <p className="text-lg text-brand-secondary/70 mb-8 max-w-2xl mx-auto">
              You've got a message to share? Please go ahead and share it. Let's
              work together to create a book that not only tells your story
              beautifully but also connects deeply with your readers and
              provokes the change you want to see.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="hero"
                size="lg"
                onClick={scrollToContact}
                className="group"
              >
                Get Your Free Quote
                <ArrowRight className="w-5 h-5 ml-1 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button variant="outline" size="lg">
                Schedule Consultation
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default BookWritingEditing;
